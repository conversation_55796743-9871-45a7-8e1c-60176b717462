import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Calendar, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface DateRangeSelectorProps {
  onDateRangeChange: (range: string) => void;
  selectedRange: string;
}

const DateRangeSelector = ({ onDateRangeChange, selectedRange }: DateRangeSelectorProps) => {
  const dateRanges = [
    { label: "Last 7 days", value: "7d" },
    { label: "Last 30 days", value: "30d" },
    { label: "Last 90 days", value: "90d" },
    { label: "Last 6 months", value: "6m" },
    { label: "Last year", value: "1y" },
    { label: "All time", value: "all" },
  ];

  const getCurrentRangeLabel = () => {
    const range = dateRanges.find(r => r.value === selectedRange);
    return range ? range.label : "Select range";
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-[180px] justify-between">
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            {getCurrentRangeLabel()}
          </div>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px]">
        {dateRanges.map((range) => (
          <DropdownMenuItem
            key={range.value}
            onClick={() => onDateRangeChange(range.value)}
            className={selectedRange === range.value ? "bg-accent" : ""}
          >
            {range.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default DateRangeSelector;
