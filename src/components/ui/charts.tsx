
import * as React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>tesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dialBar,
  Composed<PERSON>hart as <PERSON><PERSON><PERSON><PERSON>om<PERSON><PERSON><PERSON>,
} from "recharts";
import { ChartContainer, ChartTooltipContent } from "./chart";

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string;
    fill?: boolean;
  }[];
}

interface SimpleChartData {
  name: string;
  value: number;
  [key: string]: any;
}

// Enhanced color palette for better visuals
const ENHANCED_COLORS = [
  '#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b',
  '#ef4444', '#ec4899', '#84cc16', '#f97316', '#3b82f6'
];

// BarChart Component
export const BarChart = ({
  className,
  data,
}: {
  className?: string;
  data: ChartData;
}) => {
  const chartData = data.labels.map((label, index) => {
    const dataPoint: Record<string, any> = { name: label };
    data.datasets.forEach((dataset) => {
      dataPoint[dataset.label] = dataset.data[index];
    });
    return dataPoint;
  });

  const chartConfig = data.datasets.reduce((acc, dataset) => {
    acc[dataset.label] = {
      label: dataset.label,
      color: Array.isArray(dataset.backgroundColor) 
        ? dataset.backgroundColor[0] 
        : dataset.backgroundColor,
    };
    return acc;
  }, {} as Record<string, any>);

  return (
    <ChartContainer config={chartConfig} className={className}>
      <RechartsBarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip
          content={<ChartTooltipContent />}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Legend />
        {data.datasets.map((dataset, index) => (
          <Bar
            key={index}
            dataKey={dataset.label}
            fill={Array.isArray(dataset.backgroundColor)
              ? dataset.backgroundColor[0]
              : dataset.backgroundColor}
            radius={[2, 2, 0, 0]}
          />
        ))}
      </RechartsBarChart>
    </ChartContainer>
  );
};

// LineChart Component
export const LineChart = ({
  className,
  data,
}: {
  className?: string;
  data: ChartData;
}) => {
  const chartData = data.labels.map((label, index) => {
    const dataPoint: Record<string, any> = { name: label };
    data.datasets.forEach((dataset) => {
      dataPoint[dataset.label] = dataset.data[index];
    });
    return dataPoint;
  });

  const chartConfig = data.datasets.reduce((acc, dataset) => {
    acc[dataset.label] = {
      label: dataset.label,
      color: dataset.borderColor || dataset.backgroundColor,
    };
    return acc;
  }, {} as Record<string, any>);

  return (
    <ChartContainer config={chartConfig} className={className}>
      <RechartsLineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="name" />
        <YAxis />
        <Tooltip
          content={<ChartTooltipContent />}
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Legend />
        {data.datasets.map((dataset, index) => (
          <Line
            key={index}
            type="monotone"
            dataKey={dataset.label}
            stroke={dataset.borderColor}
            strokeWidth={3}
            activeDot={{
              r: 6,
              fill: dataset.borderColor,
              stroke: '#fff',
              strokeWidth: 2
            }}
            dot={{
              r: 4,
              fill: dataset.borderColor,
              strokeWidth: 0
            }}
          />
        ))}
      </RechartsLineChart>
    </ChartContainer>
  );
};

// PieChart Component
export const PieChart = ({
  className,
  data,
}: {
  className?: string;
  data: ChartData;
}) => {
  const chartData = data.labels.map((label, index) => ({
    name: label,
    value: data.datasets[0].data[index],
  }));

  const chartConfig = data.labels.reduce((acc, label, index) => {
    acc[label] = {
      label,
      color: Array.isArray(data.datasets[0].backgroundColor) 
        ? data.datasets[0].backgroundColor[index] 
        : data.datasets[0].backgroundColor,
    };
    return acc;
  }, {} as Record<string, any>);

  const COLORS = Array.isArray(data.datasets[0].backgroundColor)
    ? data.datasets[0].backgroundColor
    : Array(data.labels.length).fill(data.datasets[0].backgroundColor || "#8884d8");

  return (
    <ChartContainer config={chartConfig} className={className}>
      <RechartsPieChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <Pie
          data={chartData}
          cx="50%"
          cy="50%"
          labelLine={false}
          outerRadius={80}
          fill="#8884d8"
          dataKey="value"
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
        >
          {chartData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
        <Legend />
      </RechartsPieChart>
    </ChartContainer>
  );
};

// AreaChart Component
export const AreaChart = ({
  className,
  data,
}: {
  className?: string;
  data: ChartData;
}) => {
  const chartData = data.labels.map((label, index) => {
    const dataPoint: Record<string, any> = { name: label };
    data.datasets.forEach((dataset) => {
      dataPoint[dataset.label] = dataset.data[index];
    });
    return dataPoint;
  });

  const chartConfig = data.datasets.reduce((acc, dataset) => {
    acc[dataset.label] = {
      label: dataset.label,
      color: dataset.borderColor || dataset.backgroundColor,
    };
    return acc;
  }, {} as Record<string, any>);

  return (
    <ChartContainer config={chartConfig} className={className}>
      <RechartsAreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <defs>
          {data.datasets.map((dataset, index) => (
            <linearGradient key={index} id={`gradient-${index}`} x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor={ENHANCED_COLORS[index % ENHANCED_COLORS.length]} stopOpacity={0.8}/>
              <stop offset="95%" stopColor={ENHANCED_COLORS[index % ENHANCED_COLORS.length]} stopOpacity={0.1}/>
            </linearGradient>
          ))}
        </defs>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis dataKey="name" stroke="#666" fontSize={12} />
        <YAxis stroke="#666" fontSize={12} />
        <Tooltip content={<ChartTooltipContent />} />
        <Legend />
        {data.datasets.map((dataset, index) => (
          <Area
            key={index}
            type="monotone"
            dataKey={dataset.label}
            stroke={ENHANCED_COLORS[index % ENHANCED_COLORS.length]}
            fill={`url(#gradient-${index})`}
            strokeWidth={2}
          />
        ))}
      </RechartsAreaChart>
    </ChartContainer>
  );
};

// Enhanced BarChart with animations and better styling
export const EnhancedBarChart = ({
  className,
  data,
}: {
  className?: string;
  data: SimpleChartData[];
}) => {
  return (
    <ChartContainer config={{}} className={className}>
      <RechartsBarChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis dataKey="name" stroke="#666" fontSize={12} />
        <YAxis stroke="#666" fontSize={12} />
        <Tooltip
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Bar
          dataKey="value"
          radius={[4, 4, 0, 0]}
          fill="#6366f1"
        >
          {data.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={ENHANCED_COLORS[index % ENHANCED_COLORS.length]} />
          ))}
        </Bar>
      </RechartsBarChart>
    </ChartContainer>
  );
};

// RadialBarChart Component for progress/percentage data
export const RadialBarChart = ({
  className,
  data,
}: {
  className?: string;
  data: SimpleChartData[];
}) => {
  return (
    <ChartContainer config={{}} className={className}>
      <RechartsRadialBarChart
        cx="50%"
        cy="50%"
        innerRadius="20%"
        outerRadius="80%"
        data={data}
        startAngle={90}
        endAngle={-270}
      >
        <RadialBar
          dataKey="value"
          cornerRadius={10}
          fill="#6366f1"
        />
        <Tooltip
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
      </RechartsRadialBarChart>
    </ChartContainer>
  );
};

// ComposedChart for multiple data types
export const ComposedChart = ({
  className,
  data,
}: {
  className?: string;
  data: SimpleChartData[];
}) => {
  return (
    <ChartContainer config={{}} className={className}>
      <RechartsComposedChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
        <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
        <XAxis dataKey="name" stroke="#666" fontSize={12} />
        <YAxis stroke="#666" fontSize={12} />
        <Tooltip
          contentStyle={{
            backgroundColor: '#fff',
            border: '1px solid #e2e8f0',
            borderRadius: '8px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
        />
        <Legend />
        <Bar dataKey="value" fill="#6366f1" radius={[4, 4, 0, 0]} />
        <Line type="monotone" dataKey="value" stroke="#ef4444" strokeWidth={2} />
      </RechartsComposedChart>
    </ChartContainer>
  );
};
