import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Eye, ThumbsUp, MessageCircle, Clock, Users, TrendingUp } from "lucide-react";

interface MetricsToggleProps {
  selectedMetrics: string[];
  onMetricsChange: (metrics: string[]) => void;
}

const MetricsToggle = ({ selectedMetrics, onMetricsChange }: MetricsToggleProps) => {
  const availableMetrics = [
    { id: "views", label: "Views", icon: Eye, color: "bg-blue-500" },
    { id: "likes", label: "Likes", icon: ThumbsUp, color: "bg-green-500" },
    { id: "comments", label: "Comments", icon: MessageCircle, color: "bg-yellow-500" },
    { id: "watchTime", label: "Watch Time", icon: Clock, color: "bg-purple-500" },
    { id: "subscribers", label: "Subscribers", icon: Users, color: "bg-red-500" },
    { id: "engagement", label: "Engagement", icon: TrendingUp, color: "bg-indigo-500" },
  ];

  const toggleMetric = (metricId: string) => {
    if (selectedMetrics.includes(metricId)) {
      onMetricsChange(selectedMetrics.filter(m => m !== metricId));
    } else {
      onMetricsChange([...selectedMetrics, metricId]);
    }
  };

  return (
    <div className="space-y-3">
      <h4 className="text-sm font-medium">Select Metrics to Display</h4>
      <div className="flex flex-wrap gap-2">
        {availableMetrics.map((metric) => {
          const Icon = metric.icon;
          const isSelected = selectedMetrics.includes(metric.id);
          
          return (
            <Button
              key={metric.id}
              variant={isSelected ? "default" : "outline"}
              size="sm"
              onClick={() => toggleMetric(metric.id)}
              className={`flex items-center gap-2 ${isSelected ? metric.color : ""}`}
            >
              <Icon className="h-3 w-3" />
              {metric.label}
            </Button>
          );
        })}
      </div>
      
      {selectedMetrics.length > 0 && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Selected:</span>
          <div className="flex gap-1">
            {selectedMetrics.map((metricId) => {
              const metric = availableMetrics.find(m => m.id === metricId);
              return metric ? (
                <Badge key={metricId} variant="secondary" className="text-xs">
                  {metric.label}
                </Badge>
              ) : null;
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default MetricsToggle;
