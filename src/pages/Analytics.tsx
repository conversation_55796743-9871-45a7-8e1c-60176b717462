import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useYouTubeAnalytics } from "@/hooks/useYouTubeAnalytics";
import { Search, Users, Eye, ThumbsUp, MessageCircle, TrendingUp, Clock, Target, BarChart3, Pie<PERSON><PERSON> as PieChartIcon, Calendar, Award, Filter, Settings } from "lucide-react";
import { toast } from "sonner";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Pie<PERSON>hart, AreaChart, EnhancedBarChart, RadialBarChart } from "@/components/ui/charts";
import DateRangeSelector from "@/components/DateRangeSelector";
import MetricsToggle from "@/components/MetricsToggle";
import ExportButton from "@/components/ExportButton";

const Analytics = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedChannel, setSelectedChannel] = useState<any>(null);
  const [channelAnalytics, setChannelAnalytics] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);

  // Interactive features state
  const [selectedDateRange, setSelectedDateRange] = useState("30d");
  const [selectedMetrics, setSelectedMetrics] = useState(["views", "likes", "comments"]);
  const [showFilters, setShowFilters] = useState(false);
  
  const {
    searchChannels,
    getChannelAnalytics,
    videos,
    totalStats,
    trendingVideos,
    getEngagementTrends,
    getBestPerformingContent,
    getViewsDistribution,
    getGrowthProjection,
    getOptimalPostingTimes
  } = useYouTubeAnalytics();

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error("Please enter a channel name to search");
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchChannels(searchQuery);
      setSearchResults(results);
      
      if (results.length === 0) {
        toast.info("No channels found for your search");
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error("Failed to search for channels");
    } finally {
      setIsSearching(false);
    }
  };

  const handleChannelSelect = async (channel: any) => {
    setSelectedChannel(channel);
    
    try {
      const analytics = await getChannelAnalytics(channel.channel_id);
      setChannelAnalytics(analytics);
      toast.success(`Loaded analytics for ${channel.channel_name}`);
    } catch (error) {
      console.error("Analytics error:", error);
      toast.error("Failed to load channel analytics");
    }
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  // Prepare enhanced analytics data
  const engagementTrends = getEngagementTrends();
  const bestPerformingContent = getBestPerformingContent();
  const viewsDistribution = getViewsDistribution();
  const growthProjection = getGrowthProjection();
  const optimalPostingTimes = getOptimalPostingTimes();

  // Prepare chart data for visualizations
  const engagementData = channelAnalytics ? {
    labels: ['Views', 'Likes', 'Comments'],
    datasets: [{
      label: 'Engagement Metrics',
      data: [channelAnalytics.views, channelAnalytics.likes, channelAnalytics.comments],
      backgroundColor: ['#3B82F6', '#10B981', '#F59E0B']
    }]
  } : null;

  const performanceData = channelAnalytics ? {
    labels: ['Engagement Rate', 'CTR', 'Avg View Duration'],
    datasets: [{
      label: 'Performance %',
      data: [channelAnalytics.engagement, channelAnalytics.ctr, channelAnalytics.avgViewDuration],
      backgroundColor: ['#8B5CF6', '#EF4444', '#06B6D4']
    }]
  } : null;

  const growthTrendData = channelAnalytics ? {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [{
      label: 'Views Growth',
      data: [
        Math.floor(channelAnalytics.views * 0.7),
        Math.floor(channelAnalytics.views * 0.85),
        Math.floor(channelAnalytics.views * 0.95),
        channelAnalytics.views
      ],
      borderColor: '#3B82F6'
    }]
  } : null;

  const metricsDistributionData = channelAnalytics ? {
    labels: ['Views', 'Subscribers', 'Videos'],
    datasets: [{
      label: 'Channel Metrics',
      data: [
        channelAnalytics.views / 1000000,
        (selectedChannel?.subscriber_count || 0) / 1000000,
        (selectedChannel?.video_count || 0) / 10
      ],
      backgroundColor: ['#F472B6', '#34D399', '#FBBF24']
    }]
  } : null;

  return (
    <div className="space-y-8 animate-fade-in">
      <div>
        <h1 className="text-2xl font-bold tracking-tight mb-2">YouTube Analytics Dashboard</h1>
        <p className="text-muted-foreground">
          Comprehensive analytics and insights for YouTube content creators.
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="content">Content Analysis</TabsTrigger>
          <TabsTrigger value="audience">Audience Insights</TabsTrigger>
          <TabsTrigger value="search">Channel Search</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {totalStats && (
            <>
              {/* Overview Stats Cards */}
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Videos</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{totalStats.videos}</div>
                    <p className="text-xs text-muted-foreground">Published videos</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatNumber(totalStats.views)}</div>
                    <p className="text-xs text-muted-foreground">Across all videos</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                    <Target className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{totalStats.engagementRate}%</div>
                    <p className="text-xs text-muted-foreground">Average engagement</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Watch Time</CardTitle>
                    <Clock className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{totalStats.watchTimeHours}h</div>
                    <p className="text-xs text-muted-foreground">Total watch time</p>
                  </CardContent>
                </Card>
              </div>

              {/* Growth Projection Chart */}
              {growthProjection.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Growth Trends</CardTitle>
                    <CardDescription>Monthly performance over the last 6 months</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <AreaChart
                      data={{
                        labels: growthProjection.map(item => item.month),
                        datasets: [
                          {
                            label: 'Views',
                            data: growthProjection.map(item => item.views),
                            borderColor: '#6366f1',
                            backgroundColor: '#6366f1',
                            fill: true
                          },
                          {
                            label: 'Engagement',
                            data: growthProjection.map(item => item.engagement),
                            borderColor: '#10b981',
                            backgroundColor: '#10b981',
                            fill: true
                          }
                        ]
                      }}
                      className="h-[300px]"
                    />
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Engagement Trends */}
          {engagementTrends.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Engagement Rate Trends</CardTitle>
                <CardDescription>Track how your audience engagement changes over time</CardDescription>
              </CardHeader>
              <CardContent>
                <LineChart
                  data={{
                    labels: engagementTrends.map(item => item.date),
                    datasets: [{
                      label: 'Engagement Rate (%)',
                      data: engagementTrends.map(item => parseFloat(item.engagementRate)),
                      borderColor: '#8b5cf6',
                      backgroundColor: '#8b5cf6'
                    }]
                  }}
                  className="h-[300px]"
                />
              </CardContent>
            </Card>
          )}

          {/* Views Distribution */}
          {viewsDistribution.length > 0 && (
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Views Distribution</CardTitle>
                  <CardDescription>How your videos perform in different view ranges</CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart
                    data={{
                      labels: viewsDistribution.map(item => item.name),
                      datasets: [{
                        label: 'Videos',
                        data: viewsDistribution.map(item => item.value),
                        backgroundColor: ['#6366f1', '#8b5cf6', '#06b6d4', '#10b981', '#f59e0b']
                      }]
                    }}
                    className="h-[300px]"
                  />
                </CardContent>
              </Card>

              {/* Optimal Posting Times */}
              {optimalPostingTimes.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Optimal Posting Times</CardTitle>
                    <CardDescription>Best times to post based on your performance</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <EnhancedBarChart
                      data={optimalPostingTimes.map(item => ({
                        name: item.hour,
                        value: item.averageViews
                      }))}
                      className="h-[300px]"
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="content" className="space-y-6">
          {/* Best Performing Content */}
          {bestPerformingContent.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Top Performing Videos</CardTitle>
                <CardDescription>Your highest engagement rate videos</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {bestPerformingContent.slice(0, 5).map((video, index) => (
                    <div key={video.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <Badge variant="secondary" className="min-w-[2rem]">#{index + 1}</Badge>
                      <div className="flex-1">
                        <h4 className="font-medium line-clamp-2">{video.title}</h4>
                        <div className="flex gap-4 text-sm text-muted-foreground mt-1">
                          <span>{formatNumber(video.views)} views</span>
                          <span>{formatNumber(video.likes)} likes</span>
                          <span>{video.engagementRate.toFixed(2)}% engagement</span>
                        </div>
                      </div>
                      <Award className="h-5 w-5 text-yellow-500" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Trending Videos */}
          {trendingVideos && trendingVideos.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Trending Videos</CardTitle>
                <CardDescription>Your most viewed content</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trendingVideos.map((video, index) => (
                    <div key={video.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                      <TrendingUp className="h-5 w-5 text-green-500" />
                      <div className="flex-1">
                        <h4 className="font-medium line-clamp-2">{video.title}</h4>
                        <div className="flex gap-4 text-sm text-muted-foreground mt-1">
                          <span>{formatNumber(video.views)} views</span>
                          <span>{formatNumber(video.likes)} likes</span>
                          <span>{formatNumber(video.comments)} comments</span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="audience" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Audience Engagement</CardTitle>
                <CardDescription>How your audience interacts with your content</CardDescription>
              </CardHeader>
              <CardContent>
                {totalStats && (
                  <RadialBarChart
                    data={[
                      { name: 'Engagement Rate', value: parseFloat(totalStats.engagementRate) }
                    ]}
                    className="h-[200px]"
                  />
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Content Performance</CardTitle>
                <CardDescription>Average metrics per video</CardDescription>
              </CardHeader>
              <CardContent>
                {totalStats && (
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Average Views</span>
                      <span className="font-medium">{formatNumber(totalStats.averageViews)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Likes</span>
                      <span className="font-medium">{formatNumber(totalStats.likes)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Comments</span>
                      <span className="font-medium">{formatNumber(totalStats.comments)}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          {/* Search Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Channel Search
          </CardTitle>
          <CardDescription>
            Enter a YouTube channel name to find and analyze
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Enter channel name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              className="flex-1"
            />
            <Button 
              onClick={handleSearch} 
              disabled={isSearching}
              className="bg-youtube-red hover:bg-youtube-darkred"
            >
              {isSearching ? "Searching..." : "Search"}
            </Button>
          </div>

          {/* Search Results */}
          {searchResults.length > 0 && (
            <div className="grid gap-3 mt-4">
              <h4 className="font-medium">Search Results:</h4>
              {searchResults.map((channel) => (
                <div
                  key={channel.id}
                  className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => handleChannelSelect(channel)}
                >
                  {channel.thumbnail && (
                    <img
                      src={channel.thumbnail}
                      alt={channel.channel_name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  )}
                  <div className="flex-1">
                    <h5 className="font-medium">{channel.channel_name}</h5>
                    <div className="text-sm text-muted-foreground flex gap-4">
                      <span>{formatNumber(channel.subscriber_count || 0)} subscribers</span>
                      <span>{formatNumber(channel.view_count || 0)} total views</span>
                      <span>{channel.video_count || 0} videos</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected Channel Analytics */}
      {selectedChannel && channelAnalytics && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-4">
                {selectedChannel.thumbnail && (
                  <img
                    src={selectedChannel.thumbnail}
                    alt={selectedChannel.channel_name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                )}
                <div>
                  <CardTitle>{selectedChannel.channel_name}</CardTitle>
                  <CardDescription>Channel Analytics Overview</CardDescription>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Analytics Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Views</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(channelAnalytics.views)}</div>
                <p className="text-xs text-muted-foreground">
                  Across all videos
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Subscribers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(selectedChannel.subscriber_count || 0)}</div>
                <p className="text-xs text-muted-foreground">
                  Total subscribers
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Engagement Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{channelAnalytics.engagement}%</div>
                <p className="text-xs text-muted-foreground">
                  Likes + comments / views
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg. View Duration</CardTitle>
                <Eye className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{channelAnalytics.avgViewDuration}m</div>
                <p className="text-xs text-muted-foreground">
                  Average watch time
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Likes</CardTitle>
                <ThumbsUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(channelAnalytics.likes)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Comments</CardTitle>
                <MessageCircle className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatNumber(channelAnalytics.comments)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CTR</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{channelAnalytics.ctr}%</div>
                <p className="text-xs text-muted-foreground">
                  Click-through rate
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid gap-6 md:grid-cols-2">
            {engagementData && (
              <Card>
                <CardHeader>
                  <CardTitle>Engagement Metrics</CardTitle>
                  <CardDescription>Views, likes, and comments breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart data={engagementData} className="h-[300px]" />
                </CardContent>
              </Card>
            )}

            {performanceData && (
              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                  <CardDescription>Engagement rate, CTR, and view duration</CardDescription>
                </CardHeader>
                <CardContent>
                  <BarChart data={performanceData} className="h-[300px]" />
                </CardContent>
              </Card>
            )}

            {growthTrendData && (
              <Card>
                <CardHeader>
                  <CardTitle>Growth Trend</CardTitle>
                  <CardDescription>Views growth over the last 4 weeks</CardDescription>
                </CardHeader>
                <CardContent>
                  <LineChart data={growthTrendData} className="h-[300px]" />
                </CardContent>
              </Card>
            )}

            {metricsDistributionData && (
              <Card>
                <CardHeader>
                  <CardTitle>Channel Metrics Distribution</CardTitle>
                  <CardDescription>Overall channel performance breakdown</CardDescription>
                </CardHeader>
                <CardContent>
                  <PieChart data={metricsDistributionData} className="h-[300px]" />
                </CardContent>
              </Card>
            )}
          </div> 
        </div>
      )}

      {!selectedChannel && searchResults.length === 0 && (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Search for YouTube Channels</h3>
              <p className="text-muted-foreground">
                Enter a channel name above to start analyzing YouTube performance metrics.
              </p>
            </div>
          </CardContent>
        </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default 